
import { <PERSON>otateCcw, HelpCircle, BarChart3, Setting<PERSON> } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface GameHeaderProps {
  wordLength: 4 | 5 | 6;
  onWordLengthChange: (length: 4 | 5 | 6) => void;
  onNewGame: () => void;
}

const GameHeader = ({ wordLength, onWordLengthChange, onNewGame }: GameHeaderProps) => {
  return (
    <div className="flex items-center justify-between py-4 border-b border-indigo-200 bg-gradient-to-r from-indigo-600 to-purple-600 shadow-lg">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" className="p-2 text-white hover:text-indigo-100 hover:bg-white/10 rounded-lg">
          <HelpCircle className="w-5 h-5" />
        </But<PERSON>>
      </div>
      
      <div className="flex items-center space-x-4">
        <h1 className="text-4xl font-bold text-white tracking-wide drop-shadow-lg">
          WORDLE
        </h1>
        <Select
          value={wordLength.toString()}
          onValueChange={(value) => onWordLengthChange(Number(value) as 4 | 5 | 6)}
        >
          <SelectTrigger className="w-20 h-8 text-xs border-white/30 bg-white/10 hover:bg-white/20 text-white rounded-lg backdrop-blur-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="4">4</SelectItem>
            <SelectItem value="5">5</SelectItem>
            <SelectItem value="6">6</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" className="p-2 text-white hover:text-indigo-100 hover:bg-white/10 rounded-lg">
          <BarChart3 className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="sm" className="p-2 text-white hover:text-indigo-100 hover:bg-white/10 rounded-lg">
          <Settings className="w-5 h-5" />
        </Button>
        <Button
          onClick={onNewGame}
          variant="ghost"
          size="sm"
          className="p-2 text-white hover:text-indigo-100 hover:bg-white/10 rounded-lg"
        >
          <RotateCcw className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
};

export default GameHeader;
