import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

// Import routes
import gamesRouter from './routes/games.js';
import wordsRouter from './routes/words.js';

// Import services for cleanup
import { GameService } from './services/gameService.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || ['http://localhost:8080', 'http://localhost:3000', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
app.use('/api/games', gamesRouter);
app.use('/api/words', wordsRouter);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Wordplay Guess the Tiles API',
    version: '1.0.0',
    endpoints: {
      games: '/api/games',
      words: '/api/words',
      health: '/health'
    },
    documentation: {
      games: {
        'POST /api/games': 'Create a new game',
        'GET /api/games/:gameId': 'Get game state',
        'POST /api/games/:gameId/guess': 'Submit a guess',
        'GET /api/games/:gameId/stats': 'Get game statistics',
        'DELETE /api/games/:gameId': 'Delete a game'
      },
      words: {
        'GET /api/words/random/:length': 'Get a random word',
        'GET /api/words/random/:length/:count': 'Get multiple random words',
        'POST /api/words/validate': 'Validate a word',
        'GET /api/words/validate/:word/:length': 'Validate a word (GET)',
        'GET /api/words/stats': 'Get word statistics',
        'POST /api/words/search': 'Search words by pattern',
        'POST /api/words/with-letters': 'Find words with specific letters',
        'POST /api/words/without-letters': 'Find words without specific letters'
      }
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.originalUrl} not found`,
    availableEndpoints: [
      'GET /',
      'GET /health',
      'POST /api/games',
      'GET /api/games/:gameId',
      'POST /api/games/:gameId/guess',
      'GET /api/games/:gameId/stats',
      'DELETE /api/games/:gameId',
      'GET /api/words/random/:length',
      'POST /api/words/validate',
      'GET /api/words/stats'
    ]
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  
  // Handle JSON parsing errors
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    return res.status(400).json({
      error: 'Invalid JSON',
      message: 'Request body contains invalid JSON'
    });
  }
  
  // Handle other errors
  res.status(error.status || 500).json({
    error: error.name || 'Internal Server Error',
    message: error.message || 'An unexpected error occurred',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Cleanup old games every hour
setInterval(() => {
  try {
    const cleanedUp = GameService.cleanupOldGames();
    if (cleanedUp > 0) {
      console.log(`Cleaned up ${cleanedUp} old games`);
    }
  } catch (error) {
    console.error('Error during automatic cleanup:', error);
  }
}, 60 * 60 * 1000); // 1 hour

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Wordplay API server running on port ${PORT}`);
  console.log(`📚 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📖 API documentation: http://localhost:${PORT}/`);
});

export default app;
