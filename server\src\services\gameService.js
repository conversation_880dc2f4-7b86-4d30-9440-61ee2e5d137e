import { v4 as uuidv4 } from 'uuid';
import { getRandomWord, isValidWord } from '../data/words.js';

// In-memory storage for game sessions
// In production, this would be replaced with a database
const games = new Map();

export class GameService {
  /**
   * Create a new game session
   * @param {number} wordLength - Length of the word (4, 5, or 6)
   * @returns {Object} Game session object
   */
  static createGame(wordLength = 5) {
    if (![4, 5, 6].includes(wordLength)) {
      throw new Error('Word length must be 4, 5, or 6');
    }

    const gameId = uuidv4();
    const targetWord = getRandomWord(wordLength);
    
    const game = {
      id: gameId,
      wordLength,
      targetWord,
      guesses: [],
      gameStatus: 'playing', // 'playing', 'won', 'lost'
      maxGuesses: 6,
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    games.set(gameId, game);
    
    // Return game without revealing the target word
    return {
      id: game.id,
      wordLength: game.wordLength,
      guesses: game.guesses,
      gameStatus: game.gameStatus,
      maxGuesses: game.maxGuesses,
      createdAt: game.createdAt
    };
  }

  /**
   * Get game state by ID
   * @param {string} gameId - Game session ID
   * @returns {Object} Game state object
   */
  static getGame(gameId) {
    const game = games.get(gameId);
    if (!game) {
      throw new Error('Game not found');
    }

    // Return game state without revealing the target word (unless game is over)
    const gameState = {
      id: game.id,
      wordLength: game.wordLength,
      guesses: game.guesses,
      gameStatus: game.gameStatus,
      maxGuesses: game.maxGuesses,
      createdAt: game.createdAt,
      lastUpdated: game.lastUpdated
    };

    // Reveal target word if game is over
    if (game.gameStatus !== 'playing') {
      gameState.targetWord = game.targetWord;
    }

    return gameState;
  }

  /**
   * Submit a guess for a game
   * @param {string} gameId - Game session ID
   * @param {string} guess - The guessed word
   * @returns {Object} Guess result with feedback
   */
  static submitGuess(gameId, guess) {
    const game = games.get(gameId);
    if (!game) {
      throw new Error('Game not found');
    }

    if (game.gameStatus !== 'playing') {
      throw new Error('Game is already finished');
    }

    const upperGuess = guess.toUpperCase();

    // Validate guess length
    if (upperGuess.length !== game.wordLength) {
      throw new Error(`Guess must be ${game.wordLength} letters long`);
    }

    // Validate guess is a real word
    if (!isValidWord(upperGuess, game.wordLength)) {
      throw new Error('Not a valid word');
    }

    // Check if word was already guessed
    if (game.guesses.some(g => g.word === upperGuess)) {
      throw new Error('Word already guessed');
    }

    // Generate feedback for the guess
    const feedback = this.generateFeedback(upperGuess, game.targetWord);
    
    // Create guess object
    const guessObject = {
      word: upperGuess,
      feedback,
      timestamp: new Date()
    };

    // Add guess to game
    game.guesses.push(guessObject);
    game.lastUpdated = new Date();

    // Check win condition
    if (upperGuess === game.targetWord) {
      game.gameStatus = 'won';
    } else if (game.guesses.length >= game.maxGuesses) {
      game.gameStatus = 'lost';
    }

    // Return guess result
    const result = {
      guess: guessObject,
      gameStatus: game.gameStatus,
      isCorrect: upperGuess === game.targetWord,
      attemptsRemaining: game.maxGuesses - game.guesses.length
    };

    // Include target word if game is over
    if (game.gameStatus !== 'playing') {
      result.targetWord = game.targetWord;
    }

    return result;
  }

  /**
   * Generate feedback for a guess
   * @param {string} guess - The guessed word
   * @param {string} target - The target word
   * @returns {Array} Array of feedback objects for each letter
   */
  static generateFeedback(guess, target) {
    const feedback = [];
    const targetLetters = target.split('');
    const guessLetters = guess.split('');
    
    // First pass: mark correct positions
    const usedTargetIndices = new Set();
    const usedGuessIndices = new Set();
    
    for (let i = 0; i < guessLetters.length; i++) {
      if (guessLetters[i] === targetLetters[i]) {
        feedback[i] = {
          letter: guessLetters[i],
          status: 'correct'
        };
        usedTargetIndices.add(i);
        usedGuessIndices.add(i);
      }
    }
    
    // Second pass: mark present letters (wrong position)
    for (let i = 0; i < guessLetters.length; i++) {
      if (usedGuessIndices.has(i)) continue;
      
      const letter = guessLetters[i];
      let found = false;
      
      for (let j = 0; j < targetLetters.length; j++) {
        if (!usedTargetIndices.has(j) && targetLetters[j] === letter) {
          feedback[i] = {
            letter,
            status: 'present'
          };
          usedTargetIndices.add(j);
          found = true;
          break;
        }
      }
      
      if (!found) {
        feedback[i] = {
          letter,
          status: 'absent'
        };
      }
    }
    
    return feedback;
  }

  /**
   * Get game statistics
   * @param {string} gameId - Game session ID
   * @returns {Object} Game statistics
   */
  static getGameStats(gameId) {
    const game = games.get(gameId);
    if (!game) {
      throw new Error('Game not found');
    }

    const stats = {
      gameId: game.id,
      wordLength: game.wordLength,
      totalGuesses: game.guesses.length,
      maxGuesses: game.maxGuesses,
      gameStatus: game.gameStatus,
      duration: new Date() - game.createdAt,
      createdAt: game.createdAt,
      lastUpdated: game.lastUpdated
    };

    if (game.gameStatus !== 'playing') {
      stats.targetWord = game.targetWord;
      stats.solved = game.gameStatus === 'won';
      if (stats.solved) {
        stats.solvedInGuesses = game.guesses.length;
      }
    }

    return stats;
  }

  /**
   * Delete a game session (cleanup)
   * @param {string} gameId - Game session ID
   * @returns {boolean} Success status
   */
  static deleteGame(gameId) {
    return games.delete(gameId);
  }

  /**
   * Get all active games (for debugging/admin purposes)
   * @returns {Array} Array of game IDs
   */
  static getAllGames() {
    return Array.from(games.keys());
  }

  /**
   * Clean up old games (games older than 24 hours)
   * @returns {number} Number of games cleaned up
   */
  static cleanupOldGames() {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    let cleanedUp = 0;

    for (const [gameId, game] of games.entries()) {
      if (game.createdAt < oneDayAgo) {
        games.delete(gameId);
        cleanedUp++;
      }
    }

    return cleanedUp;
  }
}
