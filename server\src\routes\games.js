import express from 'express';
import { GameService } from '../services/gameService.js';

const router = express.Router();

/**
 * POST /api/games
 * Create a new game session
 */
router.post('/', async (req, res) => {
  try {
    const { wordLength = 5 } = req.body;
    
    // Validate word length
    if (![4, 5, 6].includes(wordLength)) {
      return res.status(400).json({
        error: 'Invalid word length',
        message: 'Word length must be 4, 5, or 6'
      });
    }

    const game = GameService.createGame(wordLength);
    
    res.status(201).json({
      success: true,
      data: game
    });
  } catch (error) {
    console.error('Error creating game:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create game'
    });
  }
});

/**
 * GET /api/games/:gameId
 * Get game state by ID
 */
router.get('/:gameId', async (req, res) => {
  try {
    const { gameId } = req.params;
    
    if (!gameId) {
      return res.status(400).json({
        error: 'Missing game ID',
        message: 'Game ID is required'
      });
    }

    const game = GameService.getGame(gameId);
    
    res.json({
      success: true,
      data: game
    });
  } catch (error) {
    if (error.message === 'Game not found') {
      return res.status(404).json({
        error: 'Game not found',
        message: 'The specified game does not exist'
      });
    }
    
    console.error('Error getting game:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve game'
    });
  }
});

/**
 * POST /api/games/:gameId/guess
 * Submit a guess for a game
 */
router.post('/:gameId/guess', async (req, res) => {
  try {
    const { gameId } = req.params;
    const { guess } = req.body;
    
    if (!gameId) {
      return res.status(400).json({
        error: 'Missing game ID',
        message: 'Game ID is required'
      });
    }

    if (!guess || typeof guess !== 'string') {
      return res.status(400).json({
        error: 'Invalid guess',
        message: 'Guess must be a non-empty string'
      });
    }

    const result = GameService.submitGuess(gameId, guess);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    if (error.message === 'Game not found') {
      return res.status(404).json({
        error: 'Game not found',
        message: 'The specified game does not exist'
      });
    }
    
    if (error.message === 'Game is already finished') {
      return res.status(400).json({
        error: 'Game finished',
        message: 'Cannot submit guess for a finished game'
      });
    }
    
    if (error.message.includes('must be') || 
        error.message === 'Not a valid word' || 
        error.message === 'Word already guessed') {
      return res.status(400).json({
        error: 'Invalid guess',
        message: error.message
      });
    }
    
    console.error('Error submitting guess:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to submit guess'
    });
  }
});

/**
 * GET /api/games/:gameId/stats
 * Get game statistics
 */
router.get('/:gameId/stats', async (req, res) => {
  try {
    const { gameId } = req.params;
    
    if (!gameId) {
      return res.status(400).json({
        error: 'Missing game ID',
        message: 'Game ID is required'
      });
    }

    const stats = GameService.getGameStats(gameId);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    if (error.message === 'Game not found') {
      return res.status(404).json({
        error: 'Game not found',
        message: 'The specified game does not exist'
      });
    }
    
    console.error('Error getting game stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve game statistics'
    });
  }
});

/**
 * DELETE /api/games/:gameId
 * Delete a game session
 */
router.delete('/:gameId', async (req, res) => {
  try {
    const { gameId } = req.params;
    
    if (!gameId) {
      return res.status(400).json({
        error: 'Missing game ID',
        message: 'Game ID is required'
      });
    }

    const deleted = GameService.deleteGame(gameId);
    
    if (!deleted) {
      return res.status(404).json({
        error: 'Game not found',
        message: 'The specified game does not exist'
      });
    }
    
    res.json({
      success: true,
      message: 'Game deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting game:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to delete game'
    });
  }
});

/**
 * POST /api/games/cleanup
 * Clean up old games (admin endpoint)
 */
router.post('/cleanup', async (req, res) => {
  try {
    const cleanedUp = GameService.cleanupOldGames();
    
    res.json({
      success: true,
      message: `Cleaned up ${cleanedUp} old games`
    });
  } catch (error) {
    console.error('Error cleaning up games:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to clean up games'
    });
  }
});

export default router;
