#!/usr/bin/env node

/**
 * Startup script for Wordplay Guess the Tiles
 * This script helps users set up and run the application
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkNodeModules() {
  const frontendModules = existsSync('./node_modules');
  const backendModules = existsSync('./server/node_modules');
  
  return { frontendModules, backendModules };
}

function runCommand(command, args, cwd = '.') {
  return new Promise((resolve, reject) => {
    log(`Running: ${command} ${args.join(' ')}`, 'cyan');
    
    const child = spawn(command, args, {
      cwd,
      stdio: 'inherit',
      shell: true
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', reject);
  });
}

async function installDependencies() {
  const { frontendModules, backendModules } = checkNodeModules();
  
  if (!frontendModules) {
    log('📦 Installing frontend dependencies...', 'yellow');
    await runCommand('npm', ['install']);
    log('✅ Frontend dependencies installed!', 'green');
  }
  
  if (!backendModules) {
    log('📦 Installing backend dependencies...', 'yellow');
    await runCommand('npm', ['install'], './server');
    log('✅ Backend dependencies installed!', 'green');
  }
  
  if (frontendModules && backendModules) {
    log('✅ All dependencies are already installed!', 'green');
  }
}

function startApplication() {
  log('🚀 Starting Wordplay Guess the Tiles...', 'magenta');
  log('', 'reset');
  log('Frontend will be available at: http://localhost:8080', 'cyan');
  log('Backend API will be available at: http://localhost:3001', 'cyan');
  log('', 'reset');
  log('Press Ctrl+C to stop the application', 'yellow');
  log('', 'reset');
  
  // Start both frontend and backend
  const child = spawn('npm', ['run', 'dev:full'], {
    stdio: 'inherit',
    shell: true
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    log('\n🛑 Shutting down...', 'yellow');
    child.kill('SIGINT');
    process.exit(0);
  });
  
  child.on('error', (error) => {
    log(`❌ Error starting application: ${error.message}`, 'red');
    process.exit(1);
  });
}

async function main() {
  try {
    log('🎮 Wordplay: Guess the Tiles Setup', 'bright');
    log('=====================================', 'bright');
    log('', 'reset');
    
    // Check if we're in the right directory
    if (!existsSync('./package.json') || !existsSync('./server')) {
      log('❌ Please run this script from the project root directory', 'red');
      process.exit(1);
    }
    
    // Install dependencies if needed
    await installDependencies();
    
    log('', 'reset');
    log('🎯 Setup complete! Starting the application...', 'green');
    log('', 'reset');
    
    // Start the application
    startApplication();
    
  } catch (error) {
    log(`❌ Setup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the script
main();
