
import { TileState } from '@/pages/Index';
import { cn } from '@/lib/utils';

interface GameTileProps {
  letter: string;
  state: TileState;
  animate?: boolean;
  delay?: number;
  revealed?: boolean;
}

const GameTile = ({ letter, state, animate = false, delay = 0, revealed = false }: GameTileProps) => {
  const getStateClasses = (state: TileState) => {
    switch (state) {
      case 'correct':
        return 'bg-gradient-to-br from-emerald-500 to-emerald-600 border-emerald-600 text-white shadow-lg ring-2 ring-emerald-200';
      case 'present':
        return 'bg-gradient-to-br from-amber-500 to-orange-500 border-orange-500 text-white shadow-lg ring-2 ring-amber-200';
      case 'absent':
        return 'bg-gradient-to-br from-slate-500 to-slate-600 border-slate-600 text-white shadow-lg';
      case 'filled':
        return 'bg-white border-indigo-400 text-slate-800 border-2 shadow-md ring-2 ring-indigo-100';
      default:
        return 'bg-white border-slate-300 text-slate-800 hover:border-indigo-300 transition-all duration-200 shadow-sm';
    }
  };

  return (
    <div
      className={cn(
        'w-16 h-16 border-2 flex items-center justify-center text-2xl font-bold uppercase transition-all duration-200',
        'relative transform-style-preserve-3d rounded-lg backdrop-blur-sm',
        getStateClasses(state),
        animate && revealed && 'animate-flip',
        letter && state === 'filled' && 'animate-bounce-small border-indigo-500 scale-105 ring-4 ring-indigo-200',
        !letter && state === 'empty' && 'border-slate-300 hover:shadow-md'
      )}
      style={{
        animationDelay: animate && revealed ? `${delay}ms` : '0ms'
      }}
    >
      <span className={cn(
        'transition-all duration-200 drop-shadow-sm',
        letter && state === 'filled' && 'scale-110 text-indigo-700'
      )}>
        {letter}
      </span>
    </div>
  );
};

export default GameTile;
