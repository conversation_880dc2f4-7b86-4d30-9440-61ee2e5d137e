# Wordplay: Guess the Tiles

A modern, full-stack Wordle clone built with React, TypeScript, and Node.js. Features a beautiful UI, comprehensive backend API, and support for 4, 5, and 6-letter words.

## 🎮 How to Play

Each guess must be a valid n-letter word (4, 5, or 6 letters).

The color of a tile will change to show you how close your guess was:

- **🟢 Green**: The letter is in the word and in the correct spot
- **🟡 Yellow**: The letter is in the word but in the wrong spot
- **⬜ Gray**: The letter is not in the word

You have 6 attempts to guess the word!

## ✨ Features

- **Multiple Word Lengths**: Play with 4, 5, or 6-letter words
- **Beautiful UI**: Modern design with smooth animations
- **Full Backend API**: Comprehensive game management and word validation
- **Real-time Feedback**: Instant validation and feedback
- **Responsive Design**: Works perfectly on desktop and mobile
- **Comprehensive Word Lists**: Thousands of valid English words
- **Game Statistics**: Track your progress and performance

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation & Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd wordplay-guess-the-tiles-main
```

2. **Install frontend dependencies**
```bash
npm install
```

3. **Install backend dependencies**
```bash
cd server
npm install
cd ..
```

4. **Start both frontend and backend**
```bash
npm run dev:full
```

This will start:
- Frontend at `http://localhost:8080`
- Backend API at `http://localhost:3001`

### Alternative: Start Separately

**Frontend only:**
```bash
npm run dev
```

**Backend only:**
```bash
cd server
npm run dev
```

## 🏗️ Architecture

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **React Query** for API state management

### Backend
- **Node.js** with Express
- **RESTful API** design
- **In-memory game sessions** (easily extensible to database)
- **Comprehensive word dictionaries**
- **Automatic session cleanup**

## 📁 Project Structure

```
wordplay-guess-the-tiles-main/
├── src/                    # Frontend source code
│   ├── components/         # React components
│   ├── pages/             # Page components
│   ├── services/          # API client
│   └── hooks/             # Custom React hooks
├── server/                # Backend source code
│   ├── src/
│   │   ├── routes/        # API routes
│   │   ├── services/      # Business logic
│   │   └── data/          # Word dictionaries
│   └── package.json
├── public/                # Static assets
└── package.json           # Frontend dependencies
```

## 🔌 API Endpoints

### Game Management
- `POST /api/games` - Create new game
- `GET /api/games/:id` - Get game state
- `POST /api/games/:id/guess` - Submit guess
- `GET /api/games/:id/stats` - Get statistics

### Word Operations
- `GET /api/words/random/:length` - Get random word
- `POST /api/words/validate` - Validate word
- `GET /api/words/stats` - Get word statistics

### Health Check
- `GET /health` - API health status

## 🎯 Game Logic

The backend implements authentic Wordle rules:

1. **Exact Match (Green)**: Letter in correct position
2. **Partial Match (Yellow)**: Letter in word, wrong position
3. **No Match (Gray)**: Letter not in word
4. **Duplicate Handling**: Proper handling of repeated letters
5. **Word Validation**: Only accepts valid English words

## 🛠️ Development

### Available Scripts

**Frontend:**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

**Backend:**
- `npm run dev` - Start with auto-reload
- `npm start` - Start production server

**Full Stack:**
- `npm run dev:full` - Start both frontend and backend

### Environment Variables

Create `server/.env` from `server/.env.example`:

```env
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:8080
```

## 🎨 Customization

### Adding New Word Lists
Edit `server/src/data/words.js` to add new words or create new length categories.

### Styling
The app uses Tailwind CSS. Modify styles in component files or extend the theme in `tailwind.config.ts`.

### Game Rules
Modify game logic in `server/src/services/gameService.js`.

## 🚀 Deployment

### Frontend
The frontend can be deployed to any static hosting service:
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

### Backend
The backend can be deployed to:
- Heroku
- Railway
- DigitalOcean App Platform
- AWS Elastic Beanstalk
- Any VPS with Node.js

### Environment Setup
1. Set `FRONTEND_URL` to your frontend domain
2. Configure CORS settings if needed
3. Set `NODE_ENV=production`

## 🧪 Testing

Run tests with:
```bash
npm test
```

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 🐛 Issues

If you encounter any issues, please file them in the [Issues](../../issues) section.

## 🙏 Acknowledgments

- Inspired by the original Wordle game by Josh Wardle
- Built with modern web technologies
- UI components from shadcn/ui
