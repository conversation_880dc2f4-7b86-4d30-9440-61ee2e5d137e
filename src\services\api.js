// API client for the Wordplay backend

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

class ApiError extends Error {
  constructor(message, status, data) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(
          data.message || 'An error occurred',
          response.status,
          data
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Network or other errors
      throw new ApiError(
        'Network error or server unavailable',
        0,
        { originalError: error.message }
      );
    }
  }

  // Game API methods
  async createGame(wordLength = 5) {
    const response = await this.request('/games', {
      method: 'POST',
      body: { wordLength },
    });
    return response.data;
  }

  async getGame(gameId) {
    const response = await this.request(`/games/${gameId}`);
    return response.data;
  }

  async submitGuess(gameId, guess) {
    const response = await this.request(`/games/${gameId}/guess`, {
      method: 'POST',
      body: { guess },
    });
    return response.data;
  }

  async getGameStats(gameId) {
    const response = await this.request(`/games/${gameId}/stats`);
    return response.data;
  }

  async deleteGame(gameId) {
    const response = await this.request(`/games/${gameId}`, {
      method: 'DELETE',
    });
    return response.data;
  }

  // Word API methods
  async getRandomWord(length) {
    const response = await this.request(`/words/random/${length}`);
    return response.data;
  }

  async getRandomWords(length, count) {
    const response = await this.request(`/words/random/${length}/${count}`);
    return response.data;
  }

  async validateWord(word, length) {
    const response = await this.request('/words/validate', {
      method: 'POST',
      body: { word, length },
    });
    return response.data;
  }

  async validateWordGet(word, length) {
    const response = await this.request(`/words/validate/${word}/${length}`);
    return response.data;
  }

  async getWordStats(length = null) {
    const endpoint = length ? `/words/stats/${length}` : '/words/stats';
    const response = await this.request(endpoint);
    return response.data;
  }

  async searchWords(pattern, length) {
    const response = await this.request('/words/search', {
      method: 'POST',
      body: { pattern, length },
    });
    return response.data;
  }

  async getWordsWithLetters(letters, length, exactMatch = false) {
    const response = await this.request('/words/with-letters', {
      method: 'POST',
      body: { letters, length, exactMatch },
    });
    return response.data;
  }

  async getWordsWithoutLetters(letters, length) {
    const response = await this.request('/words/without-letters', {
      method: 'POST',
      body: { letters, length },
    });
    return response.data;
  }

  // Health check
  async healthCheck() {
    const response = await fetch(`${this.baseURL.replace('/api', '')}/health`);
    return response.json();
  }
}

// Create and export a singleton instance
const apiClient = new ApiClient();

export { ApiClient, ApiError };
export default apiClient;
