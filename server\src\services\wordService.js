import { getRandomWord, is<PERSON><PERSON><PERSON><PERSON>ord, WORD_LISTS } from '../data/words.js';

export class WordService {
  /**
   * Get a random word of specified length
   * @param {number} length - Word length (4, 5, or 6)
   * @returns {string} Random word
   */
  static getRandomWord(length) {
    if (![4, 5, 6].includes(length)) {
      throw new Error('Word length must be 4, 5, or 6');
    }
    return getRandomWord(length);
  }

  /**
   * Validate if a word exists in the dictionary
   * @param {string} word - Word to validate
   * @param {number} length - Expected word length
   * @returns {boolean} True if word is valid
   */
  static validateWord(word, length) {
    if (!word || typeof word !== 'string') {
      return false;
    }

    if (![4, 5, 6].includes(length)) {
      return false;
    }

    const upperWord = word.toUpperCase();
    
    // Check length
    if (upperWord.length !== length) {
      return false;
    }

    // Check if word contains only letters
    if (!/^[A-Z]+$/.test(upperWord)) {
      return false;
    }

    return isValid<PERSON>ord(upperWord, length);
  }

  /**
   * Get word list statistics
   * @param {number} length - Word length (optional, if not provided returns stats for all lengths)
   * @returns {Object} Statistics about word lists
   */
  static getWordListStats(length) {
    if (length && ![4, 5, 6].includes(length)) {
      throw new Error('Word length must be 4, 5, or 6');
    }

    if (length) {
      return {
        length,
        count: WORD_LISTS[length].length,
        sampleWords: WORD_LISTS[length].slice(0, 5)
      };
    }

    return {
      4: {
        count: WORD_LISTS[4].length,
        sampleWords: WORD_LISTS[4].slice(0, 5)
      },
      5: {
        count: WORD_LISTS[5].length,
        sampleWords: WORD_LISTS[5].slice(0, 5)
      },
      6: {
        count: WORD_LISTS[6].length,
        sampleWords: WORD_LISTS[6].slice(0, 5)
      },
      total: WORD_LISTS[4].length + WORD_LISTS[5].length + WORD_LISTS[6].length
    };
  }

  /**
   * Get multiple random words
   * @param {number} length - Word length
   * @param {number} count - Number of words to return
   * @returns {Array} Array of random words
   */
  static getRandomWords(length, count = 1) {
    if (![4, 5, 6].includes(length)) {
      throw new Error('Word length must be 4, 5, or 6');
    }

    if (count < 1 || count > 100) {
      throw new Error('Count must be between 1 and 100');
    }

    const words = [];
    const wordList = WORD_LISTS[length];
    
    for (let i = 0; i < count; i++) {
      let word;
      do {
        word = wordList[Math.floor(Math.random() * wordList.length)];
      } while (words.includes(word) && words.length < wordList.length);
      
      words.push(word);
    }

    return words;
  }

  /**
   * Search for words matching a pattern
   * @param {string} pattern - Pattern to match (use _ for unknown letters)
   * @param {number} length - Word length
   * @returns {Array} Array of matching words
   */
  static searchWords(pattern, length) {
    if (![4, 5, 6].includes(length)) {
      throw new Error('Word length must be 4, 5, or 6');
    }

    if (!pattern || pattern.length !== length) {
      throw new Error(`Pattern must be ${length} characters long`);
    }

    const regex = new RegExp('^' + pattern.replace(/_/g, '[A-Z]') + '$');
    return WORD_LISTS[length].filter(word => regex.test(word));
  }

  /**
   * Get words containing specific letters
   * @param {string} letters - Letters that must be in the word
   * @param {number} length - Word length
   * @param {boolean} exactMatch - If true, word must contain exactly these letters
   * @returns {Array} Array of matching words
   */
  static getWordsWithLetters(letters, length, exactMatch = false) {
    if (![4, 5, 6].includes(length)) {
      throw new Error('Word length must be 4, 5, or 6');
    }

    const upperLetters = letters.toUpperCase();
    const wordList = WORD_LISTS[length];

    if (exactMatch) {
      return wordList.filter(word => {
        const wordLetters = word.split('').sort().join('');
        const searchLetters = upperLetters.split('').sort().join('');
        return wordLetters === searchLetters;
      });
    } else {
      return wordList.filter(word => {
        return upperLetters.split('').every(letter => word.includes(letter));
      });
    }
  }

  /**
   * Get words NOT containing specific letters
   * @param {string} letters - Letters that must NOT be in the word
   * @param {number} length - Word length
   * @returns {Array} Array of matching words
   */
  static getWordsWithoutLetters(letters, length) {
    if (![4, 5, 6].includes(length)) {
      throw new Error('Word length must be 4, 5, or 6');
    }

    const upperLetters = letters.toUpperCase();
    const wordList = WORD_LISTS[length];

    return wordList.filter(word => {
      return !upperLetters.split('').some(letter => word.includes(letter));
    });
  }
}
