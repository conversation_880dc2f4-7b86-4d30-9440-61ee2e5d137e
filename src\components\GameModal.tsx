
import { GameStatus } from '@/pages/Index';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface GameModalProps {
  isOpen: boolean;
  onClose: () => void;
  gameStatus: GameStatus;
  guesses: string[];
  targetWord: string;
  onNewGame: () => void;
}

const GameModal = ({ isOpen, onClose, gameStatus, guesses, targetWord, onNewGame }: GameModalProps) => {
  const handleNewGame = () => {
    onNewGame();
    onClose();
  };
  
  const getMessageBasedOnGuesses = () => {
    if (gameStatus === 'won') {
      if (guesses.length === 1) return "Genius";
      if (guesses.length === 2) return "Magnificent";
      if (guesses.length === 3) return "Impressive";
      if (guesses.length === 4) return "Splendid";
      if (guesses.length === 5) return "Great";
      return "Phew";
    }
    return "";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-gradient-to-br from-white to-indigo-50 border-indigo-200 shadow-2xl backdrop-blur-lg">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            {gameStatus === 'won' ? getMessageBasedOnGuesses() : 'Better luck next time!'}
          </DialogTitle>
          <DialogDescription className="text-center space-y-4 pt-4">
            {gameStatus === 'won' ? (
              <div>
                <p className="text-lg text-slate-700">
                  You solved it in {guesses.length} {guesses.length === 1 ? 'guess' : 'guesses'}!
                </p>
                <div className="flex justify-center space-x-2 mt-4">
                  {Array.from({ length: guesses.length }).map((_, i) => (
                    <div key={i} className="w-3 h-12 bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-sm shadow-md"></div>
                  ))}
                  {Array.from({ length: 6 - guesses.length }).map((_, i) => (
                    <div key={i + guesses.length} className="w-3 h-12 bg-slate-300 rounded-sm shadow-sm"></div>
                  ))}
                </div>
              </div>
            ) : (
              <div>
                <p className="text-lg mb-2 text-slate-700">The word was:</p>
                <p className="text-3xl font-bold bg-gradient-to-r from-emerald-500 to-emerald-600 bg-clip-text text-transparent tracking-wider">{targetWord}</p>
              </div>
            )}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex justify-center space-x-4 mt-6">
          <Button 
            onClick={handleNewGame} 
            className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-lg px-8 py-6 h-auto shadow-lg hover:shadow-xl transition-all duration-200 ring-2 ring-indigo-200"
          >
            Play Again
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GameModal;
