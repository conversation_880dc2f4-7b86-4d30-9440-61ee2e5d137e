import express from 'express';
import { WordService } from '../services/wordService.js';

const router = express.Router();

/**
 * GET /api/words/random/:length
 * Get a random word of specified length
 */
router.get('/random/:length', async (req, res) => {
  try {
    const length = parseInt(req.params.length);
    
    if (![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid word length',
        message: 'Word length must be 4, 5, or 6'
      });
    }

    const word = WordService.getRandomWord(length);
    
    res.json({
      success: true,
      data: {
        word,
        length
      }
    });
  } catch (error) {
    console.error('Error getting random word:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get random word'
    });
  }
});

/**
 * GET /api/words/random/:length/:count
 * Get multiple random words of specified length
 */
router.get('/random/:length/:count', async (req, res) => {
  try {
    const length = parseInt(req.params.length);
    const count = parseInt(req.params.count);
    
    if (![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid word length',
        message: 'Word length must be 4, 5, or 6'
      });
    }

    if (count < 1 || count > 100) {
      return res.status(400).json({
        error: 'Invalid count',
        message: 'Count must be between 1 and 100'
      });
    }

    const words = WordService.getRandomWords(length, count);
    
    res.json({
      success: true,
      data: {
        words,
        length,
        count: words.length
      }
    });
  } catch (error) {
    console.error('Error getting random words:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get random words'
    });
  }
});

/**
 * POST /api/words/validate
 * Validate if a word exists in the dictionary
 */
router.post('/validate', async (req, res) => {
  try {
    const { word, length } = req.body;
    
    if (!word || typeof word !== 'string') {
      return res.status(400).json({
        error: 'Invalid word',
        message: 'Word must be a non-empty string'
      });
    }

    if (!length || ![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid length',
        message: 'Length must be 4, 5, or 6'
      });
    }

    const isValid = WordService.validateWord(word, length);
    
    res.json({
      success: true,
      data: {
        word: word.toUpperCase(),
        length,
        isValid
      }
    });
  } catch (error) {
    console.error('Error validating word:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to validate word'
    });
  }
});

/**
 * GET /api/words/validate/:word/:length
 * Validate if a word exists in the dictionary (GET version)
 */
router.get('/validate/:word/:length', async (req, res) => {
  try {
    const { word } = req.params;
    const length = parseInt(req.params.length);
    
    if (!word || typeof word !== 'string') {
      return res.status(400).json({
        error: 'Invalid word',
        message: 'Word must be a non-empty string'
      });
    }

    if (![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid length',
        message: 'Length must be 4, 5, or 6'
      });
    }

    const isValid = WordService.validateWord(word, length);
    
    res.json({
      success: true,
      data: {
        word: word.toUpperCase(),
        length,
        isValid
      }
    });
  } catch (error) {
    console.error('Error validating word:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to validate word'
    });
  }
});

/**
 * GET /api/words/stats
 * Get word list statistics
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = WordService.getWordListStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error getting word stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get word statistics'
    });
  }
});

/**
 * GET /api/words/stats/:length
 * Get word list statistics for specific length
 */
router.get('/stats/:length', async (req, res) => {
  try {
    const length = parseInt(req.params.length);
    
    if (![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid word length',
        message: 'Word length must be 4, 5, or 6'
      });
    }

    const stats = WordService.getWordListStats(length);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error getting word stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get word statistics'
    });
  }
});

/**
 * POST /api/words/search
 * Search for words matching a pattern
 */
router.post('/search', async (req, res) => {
  try {
    const { pattern, length } = req.body;
    
    if (!pattern || typeof pattern !== 'string') {
      return res.status(400).json({
        error: 'Invalid pattern',
        message: 'Pattern must be a non-empty string'
      });
    }

    if (!length || ![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid length',
        message: 'Length must be 4, 5, or 6'
      });
    }

    const words = WordService.searchWords(pattern, length);
    
    res.json({
      success: true,
      data: {
        pattern,
        length,
        matches: words,
        count: words.length
      }
    });
  } catch (error) {
    console.error('Error searching words:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to search words'
    });
  }
});

/**
 * POST /api/words/with-letters
 * Get words containing specific letters
 */
router.post('/with-letters', async (req, res) => {
  try {
    const { letters, length, exactMatch = false } = req.body;
    
    if (!letters || typeof letters !== 'string') {
      return res.status(400).json({
        error: 'Invalid letters',
        message: 'Letters must be a non-empty string'
      });
    }

    if (!length || ![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid length',
        message: 'Length must be 4, 5, or 6'
      });
    }

    const words = WordService.getWordsWithLetters(letters, length, exactMatch);
    
    res.json({
      success: true,
      data: {
        letters: letters.toUpperCase(),
        length,
        exactMatch,
        matches: words,
        count: words.length
      }
    });
  } catch (error) {
    console.error('Error getting words with letters:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get words with letters'
    });
  }
});

/**
 * POST /api/words/without-letters
 * Get words NOT containing specific letters
 */
router.post('/without-letters', async (req, res) => {
  try {
    const { letters, length } = req.body;
    
    if (!letters || typeof letters !== 'string') {
      return res.status(400).json({
        error: 'Invalid letters',
        message: 'Letters must be a non-empty string'
      });
    }

    if (!length || ![4, 5, 6].includes(length)) {
      return res.status(400).json({
        error: 'Invalid length',
        message: 'Length must be 4, 5, or 6'
      });
    }

    const words = WordService.getWordsWithoutLetters(letters, length);
    
    res.json({
      success: true,
      data: {
        excludedLetters: letters.toUpperCase(),
        length,
        matches: words,
        count: words.length
      }
    });
  } catch (error) {
    console.error('Error getting words without letters:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get words without letters'
    });
  }
});

export default router;
