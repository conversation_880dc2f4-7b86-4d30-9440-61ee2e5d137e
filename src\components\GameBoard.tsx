
import { TileState, GameStatus } from '@/pages/Index';
import GameTile from '@/components/GameTile';
import { useState, useEffect } from 'react';

interface GameBoardProps {
  wordLength: number;
  maxGuesses: number;
  guesses: string[];
  currentGuess: string;
  currentRow: number;
  targetWord: string;
  gameStatus: GameStatus;
}

const GameBoard = ({
  wordLength,
  maxGuesses,
  guesses,
  currentGuess,
  currentRow,
  targetWord,
  gameStatus
}: GameBoardProps) => {
  const [revealedRows, setRevealedRows] = useState<Set<number>>(new Set());

  useEffect(() => {
    if (guesses.length > revealedRows.size) {
      const timer = setTimeout(() => {
        setRevealedRows(prev => new Set([...prev, guesses.length - 1]));
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [guesses.length, revealedRows.size]);

  const getTileState = (rowIndex: number, colIndex: number): TileState => {
    const guess = guesses[rowIndex];
    if (!guess) return 'empty';

    const letter = guess[colIndex];
    const targetLetter = targetWord[colIndex];

    if (letter === targetLetter) {
      return 'correct';
    } else if (targetWord.includes(letter)) {
      // Check if this letter appears in the correct position elsewhere
      const letterCount = targetWord.split('').filter(l => l === letter).length;
      const correctPositions = guess.split('').filter((l, i) => l === letter && targetWord[i] === letter).length;
      const currentPosition = guess.split('').slice(0, colIndex).filter(l => l === letter).length;
      
      if (currentPosition < letterCount - correctPositions) {
        return 'present';
      }
      return 'absent';
    } else {
      return 'absent';
    }
  };

  const getTileLetter = (rowIndex: number, colIndex: number): string => {
    if (rowIndex < guesses.length) {
      return guesses[rowIndex][colIndex] || '';
    } else if (rowIndex === currentRow && colIndex < currentGuess.length) {
      return currentGuess[colIndex];
    }
    return '';
  };

  const shouldAnimate = (rowIndex: number): boolean => {
    return revealedRows.has(rowIndex);
  };

  return (
    <div className="flex flex-col space-y-1 py-4">
      {Array.from({ length: maxGuesses }, (_, rowIndex) => (
        <div key={rowIndex} className="flex justify-center space-x-1">
          {Array.from({ length: wordLength }, (_, colIndex) => (
            <GameTile
              key={colIndex}
              letter={getTileLetter(rowIndex, colIndex)}
              state={rowIndex < guesses.length ? getTileState(rowIndex, colIndex) : 
                     (rowIndex === currentRow && colIndex < currentGuess.length) ? 'filled' : 'empty'}
              animate={rowIndex < guesses.length}
              revealed={shouldAnimate(rowIndex)}
              delay={colIndex * 150}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

export default GameBoard;
