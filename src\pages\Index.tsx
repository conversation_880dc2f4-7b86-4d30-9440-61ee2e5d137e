import { useState, useEffect } from 'react';
import GameBoard from '@/components/GameBoard';
import Keyboard from '@/components/Keyboard';
import GameHeader from '@/components/GameHeader';
import GameModal from '@/components/GameModal';
import { useToast } from '@/hooks/use-toast';
import apiClient, { ApiError } from '@/services/api';

export type GameStatus = 'playing' | 'won' | 'lost';
export type TileState = 'empty' | 'filled' | 'correct' | 'present' | 'absent';

const Index = () => {
  const [wordLength, setWordLength] = useState<4 | 5 | 6>(5);
  const [targetWord, setTargetWord] = useState('');
  const [currentGuess, setCurrentGuess] = useState('');
  const [guesses, setGuesses] = useState<string[]>([]);
  const [gameStatus, setGameStatus] = useState<GameStatus>('playing');
  const [currentRow, setCurrentRow] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [letterStates, setLetterStates] = useState<Record<string, TileState>>({});
  const [gameId, setGameId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const maxGuesses = 6;

  useEffect(() => {
    startNewGame();
  }, [wordLength]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (gameStatus !== 'playing') return;

      const key = event.key.toUpperCase();

      if (key === 'ENTER') {
        submitGuess();
      } else if (key === 'BACKSPACE') {
        setCurrentGuess(currentGuess.slice(0, -1));
      } else if (/^[A-Z]$/.test(key)) {
        if (currentGuess.length < wordLength) {
          setCurrentGuess(currentGuess + key);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentGuess, gameStatus, wordLength]);

  const startNewGame = async () => {
    setIsLoading(true);
    try {
      const game = await apiClient.createGame(wordLength);
      setGameId(game.id);
      setTargetWord(''); // Target word is hidden until game ends
      setCurrentGuess('');
      setGuesses([]);
      setGameStatus('playing');
      setCurrentRow(0);
      setShowModal(false);
      setLetterStates({});

      toast({
        title: "New Game Started",
        description: `${wordLength}-letter word game created!`,
      });
    } catch (error) {
      console.error('Error starting new game:', error);
      toast({
        title: "Error",
        description: error instanceof ApiError ? error.message : "Failed to start new game",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const isValidWord = async (word: string) => {
    try {
      const result = await apiClient.validateWord(word, wordLength);
      return result.isValid;
    } catch (error) {
      console.error('Error validating word:', error);
      return false;
    }
  };

  const updateLetterStates = (guess: string) => {
    const newStates = { ...letterStates };

    for (let i = 0; i < guess.length; i++) {
      const letter = guess[i];
      const targetLetter = targetWord[i];

      if (letter === targetLetter) {
        newStates[letter] = 'correct';
      } else if (targetWord.includes(letter) && newStates[letter] !== 'correct') {
        newStates[letter] = 'present';
      } else if (!targetWord.includes(letter)) {
        newStates[letter] = 'absent';
      }
    }

    setLetterStates(newStates);
  };

  const updateLetterStatesFromFeedback = (feedback: Array<{letter: string, status: string}>) => {
    const newStates = { ...letterStates };

    feedback.forEach(({ letter, status }) => {
      if (status === 'correct') {
        newStates[letter] = 'correct';
      } else if (status === 'present' && newStates[letter] !== 'correct') {
        newStates[letter] = 'present';
      } else if (status === 'absent' && !newStates[letter]) {
        newStates[letter] = 'absent';
      }
    });

    setLetterStates(newStates);
  };

  const submitGuess = async () => {
    if (!gameId) {
      toast({
        title: "Error",
        description: "No active game session",
        variant: "destructive",
      });
      return;
    }

    if (currentGuess.length !== wordLength) {
      toast({
        title: "Not enough letters",
        description: `Word must be ${wordLength} letters long`,
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await apiClient.submitGuess(gameId, currentGuess);

      // Update game state based on API response
      const newGuesses = [...guesses, result.guess.word];
      setGuesses(newGuesses);

      // Update letter states based on feedback
      updateLetterStatesFromFeedback(result.guess.feedback);
      setCurrentGuess('');
      setGameStatus(result.gameStatus);

      if (result.gameStatus === 'won') {
        setTargetWord(result.targetWord);
        setTimeout(() => setShowModal(true), 1800);
        toast({
          title: "Congratulations!",
          description: `You won in ${newGuesses.length} guess${newGuesses.length === 1 ? '' : 'es'}!`,
        });
      } else if (result.gameStatus === 'lost') {
        setTargetWord(result.targetWord);
        setTimeout(() => setShowModal(true), 1800);
        toast({
          title: "Game Over",
          description: `The word was ${result.targetWord}`,
          variant: "destructive",
        });
      } else {
        setCurrentRow(currentRow + 1);
      }
    } catch (error) {
      console.error('Error submitting guess:', error);
      toast({
        title: "Error",
        description: error instanceof ApiError ? error.message : "Failed to submit guess",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (key: string) => {
    if (gameStatus !== 'playing') return;

    if (key === 'ENTER') {
      submitGuess();
    } else if (key === 'BACKSPACE') {
      setCurrentGuess(currentGuess.slice(0, -1));
    } else if (key.length === 1 && /[A-Z]/.test(key)) {
      if (currentGuess.length < wordLength) {
        setCurrentGuess(currentGuess + key);
      }
    }
  };

  const getLetterState = (letter: string): TileState => {
    return letterStates[letter] || 'empty';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-0 max-w-md flex flex-col h-screen bg-white shadow-2xl border-x border-gray-200">
        <GameHeader
          wordLength={wordLength}
          onWordLengthChange={(length) => {
            if (gameStatus === 'playing') {
              const confirm = window.confirm("Are you sure you want to start a new game with a different word length?");
              if (!confirm) return;
            }
            setWordLength(length);
          }}
          onNewGame={startNewGame}
        />

        <div className="flex-1 flex flex-col justify-between bg-white px-4 py-6">
          <div className="flex-1 flex items-start justify-center pt-4">
            <GameBoard
              wordLength={wordLength}
              maxGuesses={maxGuesses}
              guesses={guesses}
              currentGuess={currentGuess}
              currentRow={currentRow}
              targetWord={targetWord}
              gameStatus={gameStatus}
            />
          </div>

          <div className="flex-shrink-0 pb-4">
            <Keyboard
              onKeyPress={handleKeyPress}
              getLetterState={getLetterState}
              disabled={gameStatus !== 'playing'}
            />
          </div>
        </div>

        <GameModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          gameStatus={gameStatus}
          guesses={guesses}
          targetWord={targetWord}
          onNewGame={startNewGame}
        />
      </div>
    </div>
  );
};

export default Index;
