
import { TileState } from '@/pages/Index';
import { cn } from '@/lib/utils';

interface KeyboardProps {
  onKeyPress: (key: string) => void;
  getLetterState: (letter: string) => TileState;
  disabled?: boolean;
}

const Keyboard = ({ onKeyPress, getLetterState, disabled = false }: KeyboardProps) => {
  const topRow = ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'];
  const middleRow = ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'];
  const bottomRow = ['Z', 'X', 'C', 'V', 'B', 'N', 'M'];

  const getKeyClasses = (letter: string) => {
    if (disabled) return 'bg-slate-200 text-slate-400 cursor-not-allowed border-slate-200';
    
    const state = getLetterState(letter);
    switch (state) {
      case 'correct':
        return 'bg-gradient-to-br from-emerald-500 to-emerald-600 text-white border-emerald-600 hover:from-emerald-600 hover:to-emerald-700 shadow-lg ring-2 ring-emerald-200';
      case 'present':
        return 'bg-gradient-to-br from-amber-500 to-orange-500 text-white border-orange-500 hover:from-orange-500 hover:to-orange-600 shadow-lg ring-2 ring-amber-200';
      case 'absent':
        return 'bg-gradient-to-br from-slate-500 to-slate-600 text-white border-slate-600 hover:from-slate-600 hover:to-slate-700 shadow-lg';
      default:
        return 'bg-gradient-to-br from-slate-100 to-slate-200 hover:from-indigo-100 hover:to-indigo-200 text-slate-800 border-slate-300 hover:border-indigo-400 active:from-indigo-200 active:to-indigo-300 shadow-md';
    }
  };

  const KeyButton = ({ children, onClick, className = '', isSpecial = false }: { 
    children: React.ReactNode; 
    onClick: () => void; 
    className?: string;
    isSpecial?: boolean;
  }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'h-12 flex items-center justify-center rounded-lg border-2 font-semibold text-sm transition-all duration-150',
        'hover:scale-105 active:scale-95 active:duration-75 backdrop-blur-sm',
        isSpecial ? 'px-2 min-w-[60px] text-xs' : 'flex-1 min-w-[38px]',
        className
      )}
    >
      {children}
    </button>
  );

  return (
    <div className="w-full max-w-lg mx-auto space-y-2 px-1">
      {/* Top Row */}
      <div className="flex justify-center space-x-1">
        {topRow.map((letter) => (
          <KeyButton
            key={letter}
            onClick={() => onKeyPress(letter)}
            className={getKeyClasses(letter)}
          >
            {letter}
          </KeyButton>
        ))}
      </div>

      {/* Middle Row */}
      <div className="flex justify-center space-x-1">
        <div className="w-4"></div>
        {middleRow.map((letter) => (
          <KeyButton
            key={letter}
            onClick={() => onKeyPress(letter)}
            className={getKeyClasses(letter)}
          >
            {letter}
          </KeyButton>
        ))}
        <div className="w-4"></div>
      </div>

      {/* Bottom Row */}
      <div className="flex justify-center space-x-1">
        <KeyButton
          onClick={() => onKeyPress('ENTER')}
          className={cn(
            disabled ? 'bg-slate-200 text-slate-400 cursor-not-allowed border-slate-200' : 
            'bg-gradient-to-br from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white border-indigo-600 shadow-lg ring-2 ring-indigo-200'
          )}
          isSpecial
        >
          ENTER
        </KeyButton>
        
        {bottomRow.map((letter) => (
          <KeyButton
            key={letter}
            onClick={() => onKeyPress(letter)}
            className={getKeyClasses(letter)}
          >
            {letter}
          </KeyButton>
        ))}
        
        <KeyButton
          onClick={() => onKeyPress('BACKSPACE')}
          className={cn(
            disabled ? 'bg-slate-200 text-slate-400 cursor-not-allowed border-slate-200' : 
            'bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-red-600 shadow-lg ring-2 ring-red-200'
          )}
          isSpecial
        >
          ⌫
        </KeyButton>
      </div>
    </div>
  );
};

export default Keyboard;
