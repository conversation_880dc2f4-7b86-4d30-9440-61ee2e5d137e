# Wordplay Backend API

A Node.js/Express backend API for the Wordplay Guess the Tiles game (Wordle clone).

## Features

- **Game Management**: Create, manage, and track game sessions
- **Word Dictionary**: Comprehensive word lists for 4, 5, and 6-letter words
- **Game Logic**: Server-side validation and feedback generation
- **RESTful API**: Clean, well-documented API endpoints
- **Real-time Feedback**: Instant word validation and guess feedback
- **Session Management**: In-memory game state management
- **Auto-cleanup**: Automatic cleanup of old game sessions

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Navigate to the server directory:
```bash
cd server
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Start the development server:
```bash
npm run dev
```

The API will be available at `http://localhost:3001`

### Production

```bash
npm start
```

## API Endpoints

### Games

- `POST /api/games` - Create a new game
- `GET /api/games/:gameId` - Get game state
- `POST /api/games/:gameId/guess` - Submit a guess
- `GET /api/games/:gameId/stats` - Get game statistics
- `DELETE /api/games/:gameId` - Delete a game

### Words

- `GET /api/words/random/:length` - Get a random word
- `GET /api/words/random/:length/:count` - Get multiple random words
- `POST /api/words/validate` - Validate a word
- `GET /api/words/validate/:word/:length` - Validate a word (GET)
- `GET /api/words/stats` - Get word statistics
- `POST /api/words/search` - Search words by pattern
- `POST /api/words/with-letters` - Find words with specific letters
- `POST /api/words/without-letters` - Find words without specific letters

### Health

- `GET /health` - Health check endpoint

## API Usage Examples

### Create a New Game

```bash
curl -X POST http://localhost:3001/api/games \
  -H "Content-Type: application/json" \
  -d '{"wordLength": 5}'
```

### Submit a Guess

```bash
curl -X POST http://localhost:3001/api/games/{gameId}/guess \
  -H "Content-Type: application/json" \
  -d '{"guess": "HELLO"}'
```

### Validate a Word

```bash
curl -X POST http://localhost:3001/api/words/validate \
  -H "Content-Type: application/json" \
  -d '{"word": "HELLO", "length": 5}'
```

## Game Logic

The backend implements the standard Wordle game logic:

- **Green (Correct)**: Letter is in the word and in the correct position
- **Yellow (Present)**: Letter is in the word but in the wrong position  
- **Gray (Absent)**: Letter is not in the word

The feedback system properly handles duplicate letters and follows Wordle's rules.

## Word Lists

The API includes comprehensive word lists:

- **4-letter words**: ~400 words
- **5-letter words**: ~300 words  
- **6-letter words**: ~300 words

All words are common English words suitable for word games.

## Environment Variables

```env
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:8080
```

## Development

### Scripts

- `npm run dev` - Start development server with auto-reload
- `npm start` - Start production server
- `npm test` - Run tests (when implemented)

### Project Structure

```
server/
├── src/
│   ├── app.js              # Express app setup
│   ├── data/
│   │   └── words.js        # Word dictionaries
│   ├── routes/
│   │   ├── games.js        # Game endpoints
│   │   └── words.js        # Word endpoints
│   └── services/
│       ├── gameService.js  # Game logic
│       └── wordService.js  # Word management
├── package.json
└── README.md
```

## Error Handling

The API returns consistent error responses:

```json
{
  "error": "Error Type",
  "message": "Human readable error message"
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `404` - Not Found
- `500` - Internal Server Error

## Security

- CORS enabled for frontend domains
- Helmet.js for security headers
- Request body size limits
- Input validation and sanitization

## Performance

- In-memory game storage for fast access
- Automatic cleanup of old games
- Efficient word lookup algorithms
- Request logging for monitoring

## Future Enhancements

- Database integration for persistence
- User authentication and profiles
- Game statistics and leaderboards
- Rate limiting
- Caching layer
- WebSocket support for real-time features
